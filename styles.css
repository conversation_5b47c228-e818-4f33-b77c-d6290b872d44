/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    transition: background 0.8s ease;
}

/* Dynamic Background Classes for Different Moods */
body.happy {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
}

body.sad {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
}

body.focused {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #55a3ff 100%);
}

body.energetic {
    background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 50%, #e17055 100%);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.title i {
    margin-right: 1rem;
    color: #ffd700;
}

.subtitle {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.9;
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Mood Grid */
.mood-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Mood Cards */
.mood-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.mood-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 1);
}

.mood-card.active {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    background: rgba(255, 255, 255, 1);
}

.mood-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.mood-card[data-mood="happy"] .mood-icon {
    color: #ffd700;
}

.mood-card[data-mood="sad"] .mood-icon {
    color: #74b9ff;
}

.mood-card[data-mood="focused"] .mood-icon {
    color: #00b894;
}

.mood-card[data-mood="energetic"] .mood-icon {
    color: #fd79a8;
}

.mood-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.mood-description {
    color: #666;
    font-size: 0.9rem;
}

/* Quote Section */
.quote-section {
    margin-bottom: 3rem;
}

.quote-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quote-icon {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    margin: 0 1rem;
}

.mood-quote {
    font-size: 1.3rem;
    font-weight: 400;
    color: white;
    font-style: italic;
    margin: 1rem 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Player Section */
.player-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    display: none;
}

.player-section.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

.song-info {
    text-align: center;
    margin-bottom: 2rem;
}

.song-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.song-artist {
    color: #666;
    font-size: 1rem;
}

/* Player Controls */
.player-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.control-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.control-btn:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.control-btn:active {
    transform: scale(0.95);
}

/* Progress Bar */
.progress-container {
    margin-top: 1rem;
}

.progress-bar {
    background: #e0e0e0;
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
    cursor: pointer;
}

.progress-fill {
    background: #667eea;
    height: 100%;
    width: 0%;
    transition: width 0.1s ease;
}

.time-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
}

/* Loading Indicator */
.loading {
    display: none;
    text-align: center;
    color: white;
    margin: 2rem 0;
}

.loading.active {
    display: block;
}

.spinner {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Error Message */
.error-message {
    display: none;
    background: rgba(231, 76, 60, 0.9);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    margin: 1rem 0;
    backdrop-filter: blur(10px);
}

.error-message.active {
    display: block;
    animation: shake 0.5s ease;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 2.5rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .mood-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
    
    .mood-card {
        padding: 1.5rem;
    }
    
    .mood-icon {
        font-size: 2.5rem;
    }
    
    .mood-quote {
        font-size: 1.1rem;
    }
    
    .player-section {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 2rem;
    }
    
    .mood-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .mood-card {
        padding: 1rem;
    }
    
    .control-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}
