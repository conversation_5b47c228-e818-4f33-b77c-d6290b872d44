/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    transition: all 0.8s ease;
    overflow-x: hidden;
}

/* Dynamic Background Classes for Different Moods */
body.happy {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
}

body.sad {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
}

body.focused {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #55a3ff 100%);
}

body.energetic {
    background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 50%, #e17055 100%);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.title i {
    margin-right: 1rem;
    color: #ffd700;
}

.subtitle {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.9;
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Mood Grid */
.mood-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Mood Buttons */
.mood-btn {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    width: 100%;
    font-family: inherit;
}

.mood-btn:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 1);
}

.mood-btn:active {
    transform: translateY(-5px);
}

.mood-btn.active {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    background: rgba(255, 255, 255, 1);
    border: 3px solid #667eea;
}

.mood-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #667eea;
    transition: all 0.3s ease;
}

.mood-btn[data-mood="happy"] .mood-icon {
    color: #ffd700;
}

.mood-btn[data-mood="sad"] .mood-icon {
    color: #74b9ff;
}

.mood-btn[data-mood="focused"] .mood-icon {
    color: #00b894;
}

.mood-btn[data-mood="energetic"] .mood-icon {
    color: #fd79a8;
}

.mood-btn:hover .mood-icon {
    transform: scale(1.1);
}

.mood-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.mood-description {
    color: #666;
    font-size: 0.9rem;
}

/* Quote Section */
.quote-section {
    margin-bottom: 3rem;
}

.quote-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quote-icon {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    margin: 0 1rem;
}

.mood-quote {
    font-size: 1.3rem;
    font-weight: 400;
    color: white;
    font-style: italic;
    margin: 1rem 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Player Section */
.player-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    margin-bottom: 3rem;
    display: none;
}

.player-section.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

.player-container {
    max-width: 100%;
}

/* Track Information */
.track-info {
    text-align: center;
    margin-bottom: 2rem;
}

.track-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.track-artist {
    color: #666;
    font-size: 1.1rem;
    font-weight: 400;
}

/* Spotify Player Container */
.spotify-player {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.spotify-player iframe {
    width: 100%;
    height: 352px;
    border: none;
    border-radius: 15px;
}

/* Instructions Section */
.instructions {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 2rem;
}

.instructions-container h3 {
    color: white;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    text-align: center;
}

.instructions-container h3 i {
    margin-right: 0.5rem;
    color: #ffd700;
}

.instructions-container ul {
    list-style: none;
    color: rgba(255, 255, 255, 0.9);
}

.instructions-container li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
}

.instructions-container li:before {
    content: "♪";
    position: absolute;
    left: 0;
    color: #ffd700;
    font-weight: bold;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.mood-btn.active {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .title {
        font-size: 2.5rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .mood-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .mood-btn {
        padding: 1.5rem;
    }

    .mood-icon {
        font-size: 2.5rem;
    }

    .mood-quote {
        font-size: 1.1rem;
    }

    .player-section {
        padding: 1.5rem;
    }

    .track-title {
        font-size: 1.5rem;
    }

    .spotify-player iframe {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 2rem;
    }

    .mood-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .mood-btn {
        padding: 1rem;
    }

    .mood-icon {
        font-size: 2rem;
    }

    .track-title {
        font-size: 1.3rem;
    }

    .spotify-player iframe {
        height: 280px;
    }

    .instructions-container {
        padding: 1rem;
    }
}
