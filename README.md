# Music Mood Player

A beautiful, responsive web application that plays music based on your current mood using the Spotify API.

## Features

- **4 Mood Options**: Happy, Sad, Focused, and Energetic
- **Spotify Integration**: Fetches real music tracks that match your selected mood
- **Audio Player**: Built-in player with play/pause, stop, and progress controls
- **Dynamic UI**: Background and quotes change based on selected mood
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Fallback System**: Includes fallback songs when Spotify API is unavailable

## Setup Instructions

### 1. Get Spotify API Credentials

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard/)
2. Log in with your Spotify account
3. Click "Create an App"
4. Fill in the app details:
   - App name: "Music Mood Player"
   - App description: "A web app that plays music based on mood"
   - Website: Your website URL (can be localhost for development)
   - Redirect URI: Not needed for this app
5. Accept the terms and create the app
6. Copy your **Client ID** and **Client Secret**

### 2. Configure the Application

1. Open `script.js` in a text editor
2. Find the `SPOTIFY_CONFIG` object at the top of the file
3. Replace the placeholder values:
   ```javascript
   const SPOTIFY_CONFIG = {
       CLIENT_ID: 'your_actual_client_id_here',
       CLIENT_SECRET: 'your_actual_client_secret_here',
       TOKEN_URL: 'https://accounts.spotify.com/api/token'
   };
   ```

### 3. Run the Application

#### Option 1: Simple File Opening
1. Double-click `index.html` to open it in your browser
2. **Note**: Some browsers may block API requests when opening files directly due to CORS policies

#### Option 2: Local Server (Recommended)
1. If you have Python installed:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```
2. Open your browser and go to `http://localhost:8000`

#### Option 3: Live Server (VS Code)
1. Install the "Live Server" extension in VS Code
2. Right-click on `index.html` and select "Open with Live Server"

## How to Use

1. **Select a Mood**: Click on one of the four mood cards (Happy, Sad, Focused, Energetic)
2. **Wait for Loading**: The app will fetch a song that matches your mood
3. **Enjoy the Music**: The song will automatically start playing
4. **Control Playback**: Use the play/pause and stop buttons to control the music
5. **Try Different Moods**: Click on different mood cards to explore various music styles

## File Structure

```
Music-Mood-Player/
├── index.html          # Main HTML file
├── styles.css          # CSS styling and responsive design
├── script.js           # JavaScript functionality and API integration
└── README.md           # This file
```

## Code Explanation

### HTML Structure (`index.html`)
- **Header**: App title and description
- **Mood Section**: Four interactive mood cards
- **Quote Section**: Dynamic quotes that change with mood
- **Player Section**: Music player controls and song information
- **Loading/Error**: User feedback elements

### CSS Styling (`styles.css`)
- **Responsive Grid**: Mood cards adapt to different screen sizes
- **Dynamic Backgrounds**: Different gradient backgrounds for each mood
- **Smooth Animations**: Hover effects, transitions, and loading animations
- **Mobile-First**: Optimized for mobile devices with touch-friendly controls

### JavaScript Functionality (`script.js`)
- **Spotify API Integration**: Fetches real music tracks based on mood
- **Audio Player**: HTML5 audio element with custom controls
- **Mood Management**: Handles mood selection and UI updates
- **Error Handling**: Graceful fallbacks when API requests fail
- **Progress Tracking**: Real-time progress bar and time display

## Mood Categories

### Happy 🙂
- **Music Style**: Pop, upbeat songs
- **Background**: Warm yellow/orange gradient
- **Quote**: Inspirational happiness quote

### Sad 😢
- **Music Style**: Indie, melancholic songs
- **Background**: Cool blue gradient
- **Quote**: Thoughtful sadness quote

### Focused 🧠
- **Music Style**: Ambient, instrumental study music
- **Background**: Green/teal gradient
- **Quote**: Productivity and focus quote

### Energetic ⚡
- **Music Style**: Electronic, high-energy workout music
- **Background**: Pink/orange gradient
- **Quote**: Energy and motivation quote

## Browser Compatibility

- **Chrome**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **Mobile Browsers**: Optimized for mobile use

## Troubleshooting

### API Issues
- **CORS Errors**: Use a local server instead of opening the file directly
- **No Music Playing**: Check your Spotify API credentials
- **Rate Limiting**: Spotify API has rate limits; wait a moment and try again

### Audio Issues
- **No Sound**: Check your device volume and browser audio permissions
- **Preview Not Available**: Some tracks don't have preview URLs; try another mood

## Security Note

⚠️ **Important**: In a production environment, never expose your Spotify Client Secret in client-side code. This implementation is for educational purposes. In a real application, you should:

1. Keep API credentials on the server
2. Use server-side authentication
3. Implement proper token management
4. Use environment variables for sensitive data

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to fork this project and submit pull requests for improvements!
