# Mood-Based Music Player

A beautiful, responsive web application that plays music based on your current mood using Spotify's Embed Player. **No API keys required!**

## Features

- **4 Mood Options**: Happy, Sad, Focused, and Energetic
- **Spotify Embed Player**: Uses Spotify's public embed player (no API keys needed)
- **Curated Playlists**: Hand-picked tracks for each mood
- **Dynamic UI**: Background gradients and quotes change based on selected mood
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Easy Customization**: Simple track ID configuration for easy updates

## Quick Start

**No setup required!** Just open `index.html` in your browser and start listening.

### Running the Application

#### Option 1: Direct File Opening
1. Double-click `index.html` to open it in your browser
2. Click on any mood button to start listening!

#### Option 2: Local Server (Recommended for development)
1. If you have Python installed:
   ```bash
   # Python 3
   python -m http.server 8000

   # Python 2
   python -m SimpleHTTPServer 8000
   ```
2. Open your browser and go to `http://localhost:8000`

#### Option 3: Live Server (VS Code)
1. Install the "Live Server" extension in VS Code
2. Right-click on `index.html` and select "Open with Live Server"

## Customizing Tracks

Want to change the songs for each mood? It's super easy!

### 1. Find Spotify Track IDs

1. Go to [Spotify Web Player](https://open.spotify.com)
2. Search for the song you want to add
3. Click on the song, then click "Share" → "Copy Song Link"
4. Extract the track ID from the URL:
   ```
   https://open.spotify.com/track/4uLU6hMCjMI75M1A2tKUQC?si=...
                                ^^^^^^^^^^^^^^^^^^^^
                                This is the track ID
   ```

### 2. Update the Track Arrays

Open `script.js` and find the `MOOD_TRACKS` object. Replace any track ID with your new one:

```javascript
const MOOD_TRACKS = {
    happy: [
        '60nZcImufyMA1MKQY3dcCH', // Happy - Pharrell Williams
        'YOUR_NEW_TRACK_ID_HERE',  // Replace with your track ID
        // ... more tracks
    ],
    // ... other moods
};
```

### 3. Update Track Information

Also update the corresponding track info in the `MOOD_DATA` object:

```javascript
const MOOD_DATA = {
    happy: {
        tracks: [
            { title: "Happy", artist: "Pharrell Williams" },
            { title: "Your New Song", artist: "Your Artist" },
            // ... more tracks
        ]
    },
    // ... other moods
};
```

## How to Use

1. **Select a Mood**: Click on one of the four mood buttons (Happy, Sad, Focused, Energetic)
2. **Enjoy the Music**: The Spotify player will load with a mood-appropriate track
3. **Control Playback**: Use the Spotify player controls to play, pause, and adjust volume
4. **Try Different Moods**: Click on different mood buttons to explore various music styles

## File Structure

```
Mood-Based-Music-Player/
├── index.html          # Main HTML structure
├── styles.css          # CSS styling and responsive design
├── script.js           # JavaScript functionality with Spotify embeds
└── README.md           # This documentation
```

## Code Explanation

### HTML Structure (`index.html`)
- **Header**: App title and description
- **Mood Section**: Four interactive mood buttons
- **Quote Section**: Dynamic quotes that change with mood
- **Player Section**: Spotify embed player and track information
- **Instructions**: How-to-use guide for users

### CSS Styling (`styles.css`)
- **Responsive Grid**: Mood buttons adapt to different screen sizes
- **Dynamic Backgrounds**: Different gradient backgrounds for each mood
- **Smooth Animations**: Hover effects, transitions, and mood-based animations
- **Mobile-First**: Optimized for mobile devices with touch-friendly controls
- **Spotify Integration**: Styled container for the Spotify embed player

### JavaScript Functionality (`script.js`)
- **Track Configuration**: Easy-to-update arrays of Spotify track IDs
- **Spotify Embed**: Dynamically loads Spotify's embed player
- **Mood Management**: Handles mood selection and UI updates
- **Random Selection**: Picks random tracks from each mood category
- **Developer Tools**: Console functions for easy track testing

## Mood Categories

### Happy 🙂
- **Music Style**: Pop, upbeat, feel-good songs
- **Background**: Warm yellow/orange gradient
- **Sample Tracks**: Happy (Pharrell Williams), Good 4 U (Olivia Rodrigo)
- **Quote**: Inspirational happiness quote

### Sad 😢
- **Music Style**: Melancholic, emotional, introspective songs
- **Background**: Cool blue gradient
- **Sample Tracks**: Someone Like You (Adele), Mad World (Gary Jules)
- **Quote**: Thoughtful sadness quote

### Focused 🧠
- **Music Style**: Ambient, instrumental, concentration music
- **Background**: Green/teal gradient
- **Sample Tracks**: Nuvole Bianche (Ludovico Einaudi), Near Light (Ólafur Arnalds)
- **Quote**: Productivity and focus quote

### Energetic ⚡
- **Music Style**: High-energy, workout, pump-up music
- **Background**: Pink/orange gradient
- **Sample Tracks**: Titanium (David Guetta), Thunder (Imagine Dragons)
- **Quote**: Energy and motivation quote

## Browser Compatibility

- **Chrome**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **Mobile Browsers**: Optimized for mobile use

## Troubleshooting

### Common Issues
- **Player Not Loading**: Check your internet connection
- **No Sound**: Ensure your device volume is up and Spotify is not blocked
- **Track Not Available**: Some tracks may not be available in your region

### Developer Console
Open your browser's developer console to see helpful debug information and use these functions:
- `getRandomTrackForMood("happy")` - Get a different happy song
- `setSpecificTrack("sad", 2)` - Play the 3rd sad song

## Advantages of This Approach

✅ **No API Keys Required** - Uses Spotify's public embed player
✅ **No Backend Needed** - Completely frontend-only solution
✅ **Full Spotify Features** - Users get the complete Spotify experience
✅ **Easy to Customize** - Simple track ID configuration
✅ **No Rate Limits** - No API quotas to worry about
✅ **Always Up-to-Date** - Spotify handles all the music streaming

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to fork this project and submit pull requests for improvements!

## Credits

- **Spotify** for their amazing embed player
- **Font Awesome** for the beautiful icons
- **Google Fonts** for the Poppins font family
