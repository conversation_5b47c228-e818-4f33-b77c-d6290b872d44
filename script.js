// Mood-Based Music Player with Spotify Embed
// No API keys required - uses Spotify's public embed player

/**
 * TRACK CONFIGURATION
 *
 * To update tracks for each mood, simply replace the Spotify track IDs below.
 *
 * How to get Spotify Track IDs:
 * 1. Go to Spotify Web Player (open.spotify.com)
 * 2. Find the song you want
 * 3. Click "Share" → "Copy Song Link"
 * 4. Extract the ID from the URL: https://open.spotify.com/track/TRACK_ID?si=...
 * 5. Replace the ID in the array below
 */

const MOOD_TRACKS = {
    happy: [
        '60nZcImufyMA1MKQY3dcCH', // Happy - <PERSON><PERSON><PERSON>
        '1aS9zpryzkdV7RXMImEJxo', // Good 4 U - <PERSON>
        '6f807x0ima9a1j3VPbc7VN', // Can't Stop the Feeling! - <PERSON>
        '4uLU6hMCjMI75M1A2tKUQC', // Uptown Funk - <PERSON> ft. <PERSON>
        '7qiZfU4dY1lWllzX7mPBI3'  // Shape of You - <PERSON>
    ],
    sad: [
        '3a1lNhkSLSkpJE4MSHpDu9', // Someone Like You - Adele
        '4fzsfWzRhPawzqhX8Qt9F3', // Mad World - Gary Jules
        '4g2hM4akpKeIhfOuIcDlZc', // Hurt - Johnny Cash
        '5CQ30WqJwcep0pYcV4AMNc', // The Sound of Silence - Disturbed
        '1BxfuPKGuaTgP7aM0Bbdwr'  // Tears in Heaven - Eric Clapton
    ],
    focused: [
        '2WfaOiMkCvy7F5fcp2zZ8L', // Ludovico Einaudi - Nuvole Bianche
        '4NHQUGzhtTLFvgF5SZesLK', // Ólafur Arnalds - Near Light
        '0NwGC0v03ysCYINtg6ns58', // Max Richter - On The Nature of Daylight
        '6ZFbXIJkuI1dVNWvzJzown', // Nils Frahm - Says
        '4VqPOruhp5EdPBeR92t6lQ'  // Kiasmos - Blurred EP
    ],
    energetic: [
        '4VqPOruhp5EdPBeR92t6lQ', // Titanium - David Guetta ft. Sia
        '2dpaYNEQHiRxtZbfNsse99', // Thunder - Imagine Dragons
        '0VjIjW4GlULA7QjEeUt5A9', // Believer - Imagine Dragons
        '4uLU6hMCjMI75M1A2tKUQC', // Uptown Funk - Mark Ronson ft. Bruno Mars
        '7qiZfU4dY1lWllzX7mPBI3'  // Shape of You - Ed Sheeran
    ]
};

// Mood quotes and track information
const MOOD_DATA = {
    happy: {
        quote: "Happiness is not something ready made. It comes from your own actions. - Dalai Lama",
        tracks: [
            { title: "Happy", artist: "Pharrell Williams" },
            { title: "Good 4 U", artist: "Olivia Rodrigo" },
            { title: "Can't Stop the Feeling!", artist: "Justin Timberlake" },
            { title: "Uptown Funk", artist: "Mark Ronson ft. Bruno Mars" },
            { title: "Shape of You", artist: "Ed Sheeran" }
        ]
    },
    sad: {
        quote: "The way sadness works is one of the strange riddles of the world. - Lemony Snicket",
        tracks: [
            { title: "Someone Like You", artist: "Adele" },
            { title: "Mad World", artist: "Gary Jules" },
            { title: "Hurt", artist: "Johnny Cash" },
            { title: "The Sound of Silence", artist: "Disturbed" },
            { title: "Tears in Heaven", artist: "Eric Clapton" }
        ]
    },
    focused: {
        quote: "Focus on being productive instead of busy. - Tim Ferriss",
        tracks: [
            { title: "Nuvole Bianche", artist: "Ludovico Einaudi" },
            { title: "Near Light", artist: "Ólafur Arnalds" },
            { title: "On The Nature of Daylight", artist: "Max Richter" },
            { title: "Says", artist: "Nils Frahm" },
            { title: "Blurred EP", artist: "Kiasmos" }
        ]
    },
    energetic: {
        quote: "Energy and persistence conquer all things. - Benjamin Franklin",
        tracks: [
            { title: "Titanium", artist: "David Guetta ft. Sia" },
            { title: "Thunder", artist: "Imagine Dragons" },
            { title: "Believer", artist: "Imagine Dragons" },
            { title: "Uptown Funk", artist: "Mark Ronson ft. Bruno Mars" },
            { title: "Shape of You", artist: "Ed Sheeran" }
        ]
    }
};

// Global variables
let currentMood = null;
let currentTrackIndex = 0;

// DOM elements
const moodButtons = document.querySelectorAll('.mood-btn');
const playerSection = document.getElementById('playerSection');
const trackTitle = document.getElementById('trackTitle');
const trackArtist = document.getElementById('trackArtist');
const spotifyPlayer = document.getElementById('spotifyPlayer');
const moodQuote = document.getElementById('moodQuote');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Mood-Based Music Player initialized');
    setupEventListeners();
});

// Set up event listeners
function setupEventListeners() {
    // Mood button click events
    moodButtons.forEach(button => {
        button.addEventListener('click', function() {
            const mood = this.dataset.mood;
            selectMood(mood);
        });
    });
}

/**
 * Main function to handle mood selection
 * @param {string} mood - The selected mood (happy, sad, focused, energetic)
 */
function selectMood(mood) {
    console.log(`Mood selected: ${mood}`);

    // Update UI for selected mood
    updateMoodUI(mood);
    currentMood = mood;

    // Get a random track for this mood
    const trackIndex = Math.floor(Math.random() * MOOD_TRACKS[mood].length);
    currentTrackIndex = trackIndex;

    // Load the Spotify embed player
    loadSpotifyPlayer(mood, trackIndex);
}

/**
 * Update the UI when a mood is selected
 * @param {string} mood - The selected mood
 */
function updateMoodUI(mood) {
    // Remove active class from all mood buttons
    moodButtons.forEach(button => button.classList.remove('active'));

    // Add active class to selected button
    const selectedButton = document.querySelector(`[data-mood="${mood}"]`);
    if (selectedButton) {
        selectedButton.classList.add('active');
    }

    // Change background gradient based on mood
    document.body.className = mood;

    // Update quote for the selected mood
    moodQuote.textContent = MOOD_DATA[mood].quote;

    // Show player section with animation
    playerSection.classList.add('active');
}

/**
 * Load Spotify embed player with the selected track
 * @param {string} mood - The selected mood
 * @param {number} trackIndex - Index of the track to play
 */
function loadSpotifyPlayer(mood, trackIndex) {
    // Get track ID and info
    const trackId = MOOD_TRACKS[mood][trackIndex];
    const trackInfo = MOOD_DATA[mood].tracks[trackIndex];

    // Update track information display
    trackTitle.textContent = trackInfo.title;
    trackArtist.textContent = trackInfo.artist;

    // Create Spotify embed iframe
    const iframe = document.createElement('iframe');
    iframe.src = `https://open.spotify.com/embed/track/${trackId}?utm_source=generator&theme=0`;
    iframe.width = '100%';
    iframe.height = '352';
    iframe.style.border = 'none';
    iframe.allowfullscreen = '';
    iframe.allow = 'autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture';
    iframe.loading = 'lazy';

    // Clear previous player and add new one
    spotifyPlayer.innerHTML = '';
    spotifyPlayer.appendChild(iframe);

    console.log(`Loaded ${trackInfo.title} by ${trackInfo.artist} for ${mood} mood`);
}

/**
 * Utility function to get a random track for a mood
 * You can call this function to get a different track for the same mood
 * @param {string} mood - The mood to get a track for
 */
function getRandomTrackForMood(mood) {
    if (!MOOD_TRACKS[mood]) {
        console.error(`Mood "${mood}" not found`);
        return;
    }

    const trackIndex = Math.floor(Math.random() * MOOD_TRACKS[mood].length);
    loadSpotifyPlayer(mood, trackIndex);
}

/**
 * Function to manually set a specific track for a mood
 * @param {string} mood - The mood
 * @param {number} trackIndex - The index of the track in the mood array
 */
function setSpecificTrack(mood, trackIndex) {
    if (!MOOD_TRACKS[mood] || !MOOD_TRACKS[mood][trackIndex]) {
        console.error(`Track not found for mood "${mood}" at index ${trackIndex}`);
        return;
    }

    updateMoodUI(mood);
    loadSpotifyPlayer(mood, trackIndex);
}

// Add some helpful console functions for developers
console.log('🎵 Mood-Based Music Player loaded!');
console.log('💡 Tip: You can call getRandomTrackForMood("happy") to get a different happy song');
console.log('💡 Tip: You can call setSpecificTrack("sad", 2) to play the 3rd sad song');
console.log('🎶 Available moods:', Object.keys(MOOD_TRACKS));
