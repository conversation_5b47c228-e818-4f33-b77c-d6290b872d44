// Music Mood Player JavaScript

// Configuration - Replace with your actual Spotify API credentials
const SPOTIFY_CONFIG = {
    CLIENT_ID: 'YOUR_SPOTIFY_CLIENT_ID', // Replace with your Spotify Client ID
    CLIENT_SECRET: 'YOUR_SPOTIFY_CLIENT_SECRET', // Replace with your Spotify Client Secret
    TOKEN_URL: 'https://accounts.spotify.com/api/token'
};

// Mood-based search queries and quotes
const MOOD_DATA = {
    happy: {
        searchQuery: 'genre:pop mood:happy',
        quote: "Happiness is not something ready made. It comes from your own actions. - <PERSON><PERSON>",
        fallbackSongs: [
            { title: "Happy", artist: "<PERSON><PERSON><PERSON>", preview: null },
            { title: "Good as Hell", artist: "<PERSON><PERSON>", preview: null },
            { title: "Can't Stop the Feeling", artist: "<PERSON>", preview: null }
        ]
    },
    sad: {
        searchQuery: 'genre:indie mood:sad',
        quote: "The way sadness works is one of the strange riddles of the world. - <PERSON><PERSON>nicket",
        fallbackSongs: [
            { title: "Someone Like You", artist: "<PERSON>", preview: null },
            { title: "Mad World", artist: "<PERSON>", preview: null },
            { title: "<PERSON>", artist: "Johnny Cash", preview: null }
        ]
    },
    focused: {
        searchQuery: 'genre:ambient instrumental study',
        quote: "Focus on being productive instead of busy. - Tim Ferriss",
        fallbackSongs: [
            { title: "Weightless", artist: "Marconi Union", preview: null },
            { title: "Clair de Lune", artist: "Claude Debussy", preview: null },
            { title: "Gymnopédie No. 1", artist: "Erik Satie", preview: null }
        ]
    },
    energetic: {
        searchQuery: 'genre:electronic workout high energy',
        quote: "Energy and persistence conquer all things. - Benjamin Franklin",
        fallbackSongs: [
            { title: "Titanium", artist: "David Guetta ft. Sia", preview: null },
            { title: "Pump It", artist: "Black Eyed Peas", preview: null },
            { title: "Thunder", artist: "Imagine Dragons", preview: null }
        ]
    }
};

// Global variables
let currentAudio = null;
let accessToken = null;
let currentMood = null;
let isPlaying = false;

// DOM elements
const moodCards = document.querySelectorAll('.mood-card');
const playerSection = document.getElementById('playerSection');
const songTitle = document.getElementById('songTitle');
const songArtist = document.getElementById('songArtist');
const playPauseBtn = document.getElementById('playPauseBtn');
const stopBtn = document.getElementById('stopBtn');
const progressFill = document.getElementById('progressFill');
const currentTimeSpan = document.getElementById('currentTime');
const durationSpan = document.getElementById('duration');
const moodQuote = document.getElementById('moodQuote');
const loadingIndicator = document.getElementById('loadingIndicator');
const errorMessage = document.getElementById('errorMessage');
const audioPlayer = document.getElementById('audioPlayer');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Music Mood Player initialized');
    setupEventListeners();
    getSpotifyToken();
});

// Set up event listeners
function setupEventListeners() {
    // Mood card click events
    moodCards.forEach(card => {
        card.addEventListener('click', function() {
            const mood = this.dataset.mood;
            selectMood(mood);
        });
    });

    // Player control events
    playPauseBtn.addEventListener('click', togglePlayPause);
    stopBtn.addEventListener('click', stopMusic);

    // Audio events
    audioPlayer.addEventListener('loadedmetadata', updateDuration);
    audioPlayer.addEventListener('timeupdate', updateProgress);
    audioPlayer.addEventListener('ended', onSongEnd);
    audioPlayer.addEventListener('error', onAudioError);

    // Progress bar click event
    document.querySelector('.progress-bar').addEventListener('click', seekAudio);
}

// Get Spotify access token
async function getSpotifyToken() {
    try {
        console.log('Attempting to get Spotify access token...');
        
        // Note: In a real application, this should be done on the server side
        // for security reasons. Client credentials should not be exposed.
        const response = await fetch(SPOTIFY_CONFIG.TOKEN_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': 'Basic ' + btoa(SPOTIFY_CONFIG.CLIENT_ID + ':' + SPOTIFY_CONFIG.CLIENT_SECRET)
            },
            body: 'grant_type=client_credentials'
        });

        if (response.ok) {
            const data = await response.json();
            accessToken = data.access_token;
            console.log('Spotify access token obtained successfully');
        } else {
            throw new Error('Failed to get access token');
        }
    } catch (error) {
        console.error('Error getting Spotify token:', error);
        console.log('Using fallback songs instead of Spotify API');
    }
}

// Select mood and fetch music
async function selectMood(mood) {
    console.log(`Mood selected: ${mood}`);
    
    // Update UI
    updateMoodUI(mood);
    currentMood = mood;
    
    // Show loading
    showLoading(true);
    hideError();
    
    try {
        let song;
        
        // Try to fetch from Spotify API if token is available
        if (accessToken) {
            song = await fetchSpotifyTrack(mood);
        }
        
        // If Spotify fails or no token, use fallback
        if (!song) {
            song = getFallbackSong(mood);
        }
        
        // Play the song
        await playMusic(song);
        
    } catch (error) {
        console.error('Error selecting mood:', error);
        showError('Failed to load music. Please try again.');
    } finally {
        showLoading(false);
    }
}

// Update UI for selected mood
function updateMoodUI(mood) {
    // Remove active class from all cards
    moodCards.forEach(card => card.classList.remove('active'));
    
    // Add active class to selected card
    const selectedCard = document.querySelector(`[data-mood="${mood}"]`);
    if (selectedCard) {
        selectedCard.classList.add('active');
    }
    
    // Change background
    document.body.className = mood;
    
    // Update quote
    moodQuote.textContent = MOOD_DATA[mood].quote;
    
    // Show player section
    playerSection.classList.add('active');
}

// Fetch track from Spotify API
async function fetchSpotifyTrack(mood) {
    try {
        const searchQuery = MOOD_DATA[mood].searchQuery;
        const response = await fetch(`https://api.spotify.com/v1/search?q=${encodeURIComponent(searchQuery)}&type=track&limit=10`, {
            headers: {
                'Authorization': 'Bearer ' + accessToken
            }
        });

        if (!response.ok) {
            throw new Error('Spotify API request failed');
        }

        const data = await response.json();
        const tracks = data.tracks.items;
        
        // Filter tracks that have preview URLs
        const tracksWithPreview = tracks.filter(track => track.preview_url);
        
        if (tracksWithPreview.length > 0) {
            // Select a random track with preview
            const randomTrack = tracksWithPreview[Math.floor(Math.random() * tracksWithPreview.length)];
            
            return {
                title: randomTrack.name,
                artist: randomTrack.artists[0].name,
                preview: randomTrack.preview_url
            };
        }
        
        return null;
    } catch (error) {
        console.error('Error fetching from Spotify:', error);
        return null;
    }
}

// Get fallback song for mood
function getFallbackSong(mood) {
    const fallbackSongs = MOOD_DATA[mood].fallbackSongs;
    const randomSong = fallbackSongs[Math.floor(Math.random() * fallbackSongs.length)];
    
    console.log('Using fallback song:', randomSong.title);
    return randomSong;
}

// Play music
async function playMusic(song) {
    try {
        // Update song info
        songTitle.textContent = song.title;
        songArtist.textContent = song.artist;
        
        if (song.preview) {
            // Load and play the preview
            audioPlayer.src = song.preview;
            audioPlayer.load();
            
            // Wait for the audio to be ready
            await new Promise((resolve, reject) => {
                audioPlayer.addEventListener('canplay', resolve, { once: true });
                audioPlayer.addEventListener('error', reject, { once: true });
            });
            
            // Play the audio
            await audioPlayer.play();
            isPlaying = true;
            updatePlayPauseButton();
            
        } else {
            // No preview available
            songTitle.textContent = song.title + ' (Preview not available)';
            songArtist.textContent = song.artist;
            showError('Preview not available for this track. Try another mood!');
        }
        
    } catch (error) {
        console.error('Error playing music:', error);
        showError('Unable to play this track. Please try again.');
    }
}

// Toggle play/pause
function togglePlayPause() {
    if (!audioPlayer.src) return;
    
    if (isPlaying) {
        audioPlayer.pause();
        isPlaying = false;
    } else {
        audioPlayer.play();
        isPlaying = true;
    }
    
    updatePlayPauseButton();
}

// Stop music
function stopMusic() {
    if (audioPlayer.src) {
        audioPlayer.pause();
        audioPlayer.currentTime = 0;
        isPlaying = false;
        updatePlayPauseButton();
        updateProgress();
    }
}

// Update play/pause button
function updatePlayPauseButton() {
    const icon = playPauseBtn.querySelector('i');
    if (isPlaying) {
        icon.className = 'fas fa-pause';
    } else {
        icon.className = 'fas fa-play';
    }
}

// Update duration display
function updateDuration() {
    if (audioPlayer.duration) {
        durationSpan.textContent = formatTime(audioPlayer.duration);
    }
}

// Update progress bar and time
function updateProgress() {
    if (audioPlayer.duration) {
        const progress = (audioPlayer.currentTime / audioPlayer.duration) * 100;
        progressFill.style.width = progress + '%';
        currentTimeSpan.textContent = formatTime(audioPlayer.currentTime);
    }
}

// Seek audio
function seekAudio(e) {
    if (!audioPlayer.duration) return;
    
    const progressBar = e.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const width = rect.width;
    const percentage = clickX / width;
    
    audioPlayer.currentTime = percentage * audioPlayer.duration;
}

// Handle song end
function onSongEnd() {
    isPlaying = false;
    updatePlayPauseButton();
    audioPlayer.currentTime = 0;
    updateProgress();
}

// Handle audio error
function onAudioError() {
    console.error('Audio playback error');
    showError('Error playing audio. Please try another mood.');
    isPlaying = false;
    updatePlayPauseButton();
}

// Format time in MM:SS
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Show/hide loading indicator
function showLoading(show) {
    if (show) {
        loadingIndicator.classList.add('active');
    } else {
        loadingIndicator.classList.remove('active');
    }
}

// Show error message
function showError(message) {
    document.getElementById('errorText').textContent = message;
    errorMessage.classList.add('active');
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        hideError();
    }, 5000);
}

// Hide error message
function hideError() {
    errorMessage.classList.remove('active');
}
