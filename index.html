<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mood-Based Music Player</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Main Container -->
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <h1 class="title">
                <i class="fas fa-music"></i>
                Mood-Based Music Player
            </h1>
            <p class="subtitle">Choose your mood and let Spotify play the perfect track</p>
        </header>

        <!-- Mood Selection Section -->
        <section class="mood-section">
            <h2 class="section-title">Select Your Mood</h2>
            <div class="mood-grid">
                <!-- Happy Mood Button -->
                <button class="mood-btn" data-mood="happy">
                    <div class="mood-icon">
                        <i class="fas fa-smile"></i>
                    </div>
                    <h3 class="mood-title">Happy</h3>
                    <p class="mood-description">Uplifting and joyful vibes</p>
                </button>

                <!-- Sad Mood Button -->
                <button class="mood-btn" data-mood="sad">
                    <div class="mood-icon">
                        <i class="fas fa-sad-tear"></i>
                    </div>
                    <h3 class="mood-title">Sad</h3>
                    <p class="mood-description">Melancholic and emotional</p>
                </button>

                <!-- Focused Mood Button -->
                <button class="mood-btn" data-mood="focused">
                    <div class="mood-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="mood-title">Focused</h3>
                    <p class="mood-description">Concentration and study</p>
                </button>

                <!-- Energetic Mood Button -->
                <button class="mood-btn" data-mood="energetic">
                    <div class="mood-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="mood-title">Energetic</h3>
                    <p class="mood-description">High-energy and pumped up</p>
                </button>
            </div>
        </section>

        <!-- Quote Section -->
        <section class="quote-section">
            <div class="quote-container">
                <i class="fas fa-quote-left quote-icon"></i>
                <p class="mood-quote" id="moodQuote">Select a mood to discover music that matches your soul</p>
                <i class="fas fa-quote-right quote-icon"></i>
            </div>
        </section>

        <!-- Music Player Section -->
        <section class="player-section" id="playerSection">
            <div class="player-container">
                <!-- Track Information -->
                <div class="track-info">
                    <h3 class="track-title" id="trackTitle">No track selected</h3>
                    <p class="track-artist" id="trackArtist">Choose a mood to start listening</p>
                </div>

                <!-- Spotify Embed Player -->
                <div class="spotify-player" id="spotifyPlayer">
                    <!-- Spotify iframe will be dynamically inserted here -->
                </div>
            </div>
        </section>

        <!-- Instructions -->
        <section class="instructions">
            <div class="instructions-container">
                <h3><i class="fas fa-info-circle"></i> How to Use</h3>
                <ul>
                    <li>Click on any mood button above</li>
                    <li>The Spotify player will load with a mood-appropriate track</li>
                    <li>Use the Spotify controls to play, pause, and adjust volume</li>
                    <li>Watch the background and quote change with your mood!</li>
                </ul>
            </div>
        </section>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>
